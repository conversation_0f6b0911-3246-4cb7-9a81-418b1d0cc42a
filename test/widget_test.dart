// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import 'package:powersights/main.dart';

void main() {
  testWidgets('PowerSights photo analysis app loads correctly', (
    WidgetTester tester,
  ) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const PowerSightsApp());

    // Verify that the app loads with the photo input screen
    expect(find.text('New Analysis'), findsOneWidget);

    // Verify that the instruction text is displayed
    expect(find.text('Add a photo'), findsOneWidget);

    // Verify that the camera and gallery buttons are present
    expect(find.text('Take Photo'), findsOneWidget);
    expect(find.text('Choose from Gallery'), findsOneWidget);
  });

  testWidgets('Theme colors are applied correctly', (
    WidgetTester tester,
  ) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const PowerSightsApp());

    // Find the app bar
    final appBar = tester.widget<AppBar>(find.byType(AppBar));

    // Verify that the app bar uses our custom theme colors
    expect(
      appBar.backgroundColor,
      equals(const Color(0xFF000000)),
    ); // Black background
    expect(
      appBar.foregroundColor,
      equals(const Color(0xFFFFFFFF)),
    ); // White text
  });
}
