/// Configuration for API endpoints and settings
class ApiConfig {
  // Hugging Face Inference API Configuration
  static const String huggingFaceInferenceUrl = 'https://api-inference.huggingface.co/models';

  // Alternative vision-language models to try
  static const List<String> visionLanguageModels = [
    'Salesforce/blip-image-captioning-large',
    'Salesforce/blip-image-captioning-base',
    'microsoft/git-large-coco',
    'nlpconnect/vit-gpt2-image-captioning',
  ];

  // Spaces endpoints (as fallback)
  static const List<String> spacesEndpoints = [
    'https://opengvlab-internvl.hf.space',
    'https://huggingface.co/spaces/OpenGVLab/InternVL',
  ];
  
  // Hugging Face API Token
  static const String huggingFaceToken = '*************************************';

  // API timeout settings (reduced with authenticated access)
  static const Duration apiTimeout = Duration(seconds: 45);
  static const Duration connectionTimeout = Duration(seconds: 20);
  
  // Default prompts for different analysis types
  static const Map<String, String> defaultPrompts = {
    'general': 'Analyze this image in detail. Describe what you see, including objects, colors, composition, and any notable features.',
    'objects': 'Identify and list all the objects visible in this image.',
    'colors': 'Describe the color palette and color scheme of this image.',
    'composition': 'Analyze the composition, lighting, and artistic elements of this image.',
    'text': 'Extract and transcribe any text visible in this image.',
  };
  
  // Model parameters
  static const Map<String, dynamic> defaultModelParams = {
    'temperature': 0.7,
    'max_tokens': 1000,
    'top_p': 0.9,
  };
  
  // Feature flags
  static const bool enableFallbackToMockData = true;
  static const bool enableRetryOnFailure = true;
  static const int maxRetryAttempts = 3;
}
