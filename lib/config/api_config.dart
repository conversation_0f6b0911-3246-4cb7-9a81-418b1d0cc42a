/// Configuration for API endpoints and settings
class ApiConfig {
  // Hugging Face InternVL API Configuration
  static const String huggingFaceBaseUrl = 'https://opengvlab-internvl.hf.space';
  static const String huggingFaceApiEndpoint = '/api/predict';
  
  // Alternative endpoints (in case the main one is down)
  static const List<String> alternativeEndpoints = [
    'https://opengvlab-internvl.hf.space',
    // Add more mirror endpoints if available
  ];
  
  // API timeout settings
  static const Duration apiTimeout = Duration(seconds: 60);
  static const Duration connectionTimeout = Duration(seconds: 30);
  
  // Default prompts for different analysis types
  static const Map<String, String> defaultPrompts = {
    'general': 'Analyze this image in detail. Describe what you see, including objects, colors, composition, and any notable features.',
    'objects': 'Identify and list all the objects visible in this image.',
    'colors': 'Describe the color palette and color scheme of this image.',
    'composition': 'Analyze the composition, lighting, and artistic elements of this image.',
    'text': 'Extract and transcribe any text visible in this image.',
  };
  
  // Model parameters
  static const Map<String, dynamic> defaultModelParams = {
    'temperature': 0.7,
    'max_tokens': 1000,
    'top_p': 0.9,
  };
  
  // Feature flags
  static const bool enableFallbackToMockData = true;
  static const bool enableRetryOnFailure = true;
  static const int maxRetryAttempts = 3;
}
