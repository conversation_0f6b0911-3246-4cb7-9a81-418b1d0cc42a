import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'package:http/http.dart' as http;
import 'package:flutter/foundation.dart';
import '../config/api_config.dart';

/// Service for interacting with Hugging Face InternVL API
/// 
/// This service handles image analysis using the InternVL model hosted on
/// Hugging Face Spaces. It converts images to base64 and sends them to the
/// Gradio API endpoint for analysis.
class HuggingFaceApiService {
  /// Current retry attempt counter
  int _retryCount = 0;
  
  /// Analyzes an image using the InternVL model
  ///
  /// [imageFile] can be either a File (mobile/desktop) or Uint8List (web)
  /// [prompt] is the question or instruction for the model (optional)
  /// [analysisType] determines the type of analysis to perform
  ///
  /// Returns a [VisionAnalysisResult] containing the analysis results
  Future<VisionAnalysisResult> analyzeImage({
    required dynamic imageFile,
    String? prompt,
    String analysisType = 'general',
  }) async {
    _retryCount = 0;
    return _analyzeImageWithRetry(imageFile, prompt, analysisType);
  }

  /// Internal method that handles retries
  Future<VisionAnalysisResult> _analyzeImageWithRetry(
    dynamic imageFile,
    String? prompt,
    String analysisType,
  ) async {
    print('🔍 Starting image analysis (attempt ${_retryCount + 1})');

    try {
      // Use provided prompt or default based on analysis type
      final effectivePrompt = prompt ?? ApiConfig.defaultPrompts[analysisType] ?? ApiConfig.defaultPrompts['general']!;
      print('📝 Using prompt: $effectivePrompt');

      // Convert image to base64
      String base64Image = await _convertImageToBase64(imageFile);
      print('🖼️ Image converted to base64 (${base64Image.length} characters)');

      // Prepare the request payload
      final payload = {
        "data": [
          base64Image,
          effectivePrompt,
          ApiConfig.defaultModelParams,
        ],
        "fn_index": 0, // Usually 0 for the main predict function
      };

      // First try Hugging Face Inference API (more reliable)
      print('🤗 Trying Hugging Face Inference API with ${ApiConfig.visionLanguageModels.length} models...');
      print('🔑 Using authenticated access with HF token');

      for (int i = 0; i < ApiConfig.visionLanguageModels.length; i++) {
        final model = ApiConfig.visionLanguageModels[i];
        final fullUrl = '${ApiConfig.huggingFaceInferenceUrl}/$model';

        try {
          print('🌐 Attempting Inference API call to: $model');

          // For Inference API, we send the image as binary data
          final imageBytes = await _getImageBytes(imageFile);

          final response = await http.post(
            Uri.parse(fullUrl),
            headers: {
              'Content-Type': 'application/octet-stream',
              'Authorization': 'Bearer ${ApiConfig.huggingFaceToken}',
            },
            body: imageBytes,
          ).timeout(ApiConfig.apiTimeout);

          print('📡 Response received: ${response.statusCode}');

          if (response.statusCode == 200) {
            print('✅ Inference API call successful! Parsing response...');
            final responseData = jsonDecode(response.body);
            print('📊 Response data: ${responseData.toString()}');
            return _parseInferenceResponse(responseData, effectivePrompt);
          } else if (response.statusCode == 503) {
            print('⚠️ Model loading (503), trying next model...');
            continue;
          } else if (response.statusCode == 429) {
            print('⚠️ Rate limited (429), trying next model...');
            continue;
          } else {
            print('❌ Inference API request failed with status ${response.statusCode}');
            print('📄 Response body: ${response.body}');
            continue;
          }
        } on SocketException catch (e) {
          print('🔌 Network error for model $i: $e');
          continue;
        } on http.ClientException catch (e) {
          print('🌐 HTTP client error for model $i: $e');
          continue;
        } on Exception catch (e) {
          print('⚠️ Unexpected error for model $i: $e');
          continue;
        }
      }

      // If Inference API fails, try Spaces endpoints as fallback
      print('📦 Inference API failed, trying Spaces endpoints...');

      for (int i = 0; i < ApiConfig.spacesEndpoints.length; i++) {
        final baseUrl = ApiConfig.spacesEndpoints[i];
        final fullUrl = '$baseUrl/api/predict';

        try {
          print('🌐 Attempting Spaces API call to: $fullUrl');

          final response = await http.post(
            Uri.parse(fullUrl),
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json',
            },
            body: jsonEncode(payload),
          ).timeout(ApiConfig.apiTimeout);

          print('📡 Response received: ${response.statusCode}');

          if (response.statusCode == 200) {
            print('✅ Spaces API call successful! Parsing response...');
            final responseData = jsonDecode(response.body);
            print('📊 Response data: ${responseData.toString().substring(0, 200)}...');
            return _parseResponse(responseData);
          } else if (response.statusCode == 503) {
            print('⚠️ Service temporarily unavailable (503), trying next endpoint...');
            continue;
          } else {
            print('❌ Spaces API request failed with status ${response.statusCode}');
            print('📄 Response body: ${response.body}');
            continue;
          }
        } on SocketException catch (e) {
          print('🔌 Network error for endpoint $i: $e');
          continue;
        } on http.ClientException catch (e) {
          print('🌐 HTTP client error for endpoint $i: $e');
          continue;
        } on Exception catch (e) {
          print('⚠️ Unexpected error for endpoint $i: $e');
          continue;
        }
      }

      // If all endpoints failed, throw an exception
      print('💥 All API endpoints failed!');
      throw ApiException('All API endpoints are currently unavailable');

    } catch (e) {
      print('🚨 Error in _analyzeImageWithRetry: $e');

      if (e is ApiException) {
        // Check if we should retry
        if (ApiConfig.enableRetryOnFailure && _retryCount < ApiConfig.maxRetryAttempts) {
          _retryCount++;
          print('🔄 Retrying in ${_retryCount * 2} seconds... (attempt $_retryCount/${ApiConfig.maxRetryAttempts})');
          await Future.delayed(Duration(seconds: _retryCount * 2)); // Exponential backoff
          return _analyzeImageWithRetry(imageFile, prompt, analysisType);
        }

        // If retries exhausted or fallback enabled, return mock data
        if (ApiConfig.enableFallbackToMockData) {
          print('🎭 Falling back to mock data');
          return VisionAnalysisResult.mock();
        }

        rethrow;
      }
      throw ApiException('Failed to analyze image: $e');
    }
  }
  
  /// Gets image bytes for Inference API
  Future<Uint8List> _getImageBytes(dynamic imageFile) async {
    try {
      if (kIsWeb) {
        // Web platform - imageFile is Uint8List
        return imageFile as Uint8List;
      } else {
        // Mobile/Desktop platform - imageFile is File
        final file = imageFile as File;
        return await file.readAsBytes();
      }
    } catch (e) {
      throw ApiException('Failed to get image bytes: $e');
    }
  }

  /// Converts image file to base64 string (for Spaces API)
  Future<String> _convertImageToBase64(dynamic imageFile) async {
    try {
      final bytes = await _getImageBytes(imageFile);

      // Convert to base64 with data URL prefix
      final base64String = base64Encode(bytes);
      return 'data:image/jpeg;base64,$base64String';
    } catch (e) {
      throw ApiException('Failed to convert image to base64: $e');
    }
  }
  
  /// Parses the Inference API response
  VisionAnalysisResult _parseInferenceResponse(dynamic responseData, String prompt) {
    try {
      String analysisText = '';

      if (responseData is List && responseData.isNotEmpty) {
        // BLIP and similar models return a list with generated_text
        final firstResult = responseData[0];
        if (firstResult is Map<String, dynamic> && firstResult.containsKey('generated_text')) {
          analysisText = firstResult['generated_text'] as String;
        } else {
          analysisText = firstResult.toString();
        }
      } else if (responseData is Map<String, dynamic>) {
        // Some models return a map directly
        analysisText = responseData['generated_text'] as String? ??
                     responseData['caption'] as String? ??
                     responseData.toString();
      } else {
        analysisText = responseData.toString();
      }

      // Add context about the prompt used
      final fullDescription = 'AI Analysis: $analysisText';

      return VisionAnalysisResult(
        description: fullDescription,
        confidence: _extractConfidence(analysisText),
        detectedObjects: _extractObjects(analysisText),
        colors: _extractColors(analysisText),
        tags: _extractTags(analysisText),
        rawResponse: {'inference_api': responseData, 'prompt': prompt},
        isMockData: false,
      );
    } catch (e) {
      throw ApiException('Failed to parse Inference API response: $e');
    }
  }

  /// Parses the Spaces API response into a structured result
  VisionAnalysisResult _parseResponse(Map<String, dynamic> responseData) {
    try {
      // The response structure may vary, but typically contains a 'data' field
      final data = responseData['data'];

      if (data is List && data.isNotEmpty) {
        final analysisText = data[0] as String? ?? 'No analysis available';

        return VisionAnalysisResult(
          description: analysisText,
          confidence: _extractConfidence(analysisText),
          detectedObjects: _extractObjects(analysisText),
          colors: _extractColors(analysisText),
          tags: _extractTags(analysisText),
          rawResponse: responseData,
          isMockData: false,
        );
      } else {
        throw ApiException('Unexpected response format');
      }
    } catch (e) {
      throw ApiException('Failed to parse API response: $e');
    }
  }
  
  /// Extracts confidence score from analysis text (basic implementation)
  double _extractConfidence(String text) {
    // This is a simple heuristic - in a real implementation,
    // you might use more sophisticated text analysis
    if (text.toLowerCase().contains('clearly') || 
        text.toLowerCase().contains('definitely')) {
      return 0.9;
    } else if (text.toLowerCase().contains('appears') || 
               text.toLowerCase().contains('seems')) {
      return 0.7;
    } else if (text.toLowerCase().contains('possibly') || 
               text.toLowerCase().contains('might')) {
      return 0.5;
    }
    return 0.8; // Default confidence
  }
  
  /// Extracts detected objects from analysis text
  List<String> _extractObjects(String text) {
    final objects = <String>[];
    final commonObjects = [
      'person', 'people', 'man', 'woman', 'child', 'baby',
      'car', 'truck', 'bus', 'motorcycle', 'bicycle',
      'dog', 'cat', 'bird', 'horse', 'cow', 'sheep',
      'tree', 'flower', 'grass', 'mountain', 'sky', 'cloud',
      'building', 'house', 'road', 'bridge', 'water', 'ocean',
      'food', 'plate', 'cup', 'bottle', 'chair', 'table',
    ];
    
    for (final object in commonObjects) {
      if (text.toLowerCase().contains(object)) {
        objects.add(object);
      }
    }
    
    return objects.take(5).toList(); // Limit to top 5
  }
  
  /// Extracts color information from analysis text
  List<String> _extractColors(String text) {
    final colors = <String>[];
    final colorWords = [
      'red', 'blue', 'green', 'yellow', 'orange', 'purple',
      'pink', 'brown', 'black', 'white', 'gray', 'grey',
    ];
    
    for (final color in colorWords) {
      if (text.toLowerCase().contains(color)) {
        colors.add(color);
      }
    }
    
    return colors.take(3).toList(); // Limit to top 3
  }
  
  /// Extracts relevant tags from analysis text
  List<String> _extractTags(String text) {
    final tags = <String>[];
    final tagWords = [
      'outdoor', 'indoor', 'nature', 'urban', 'portrait',
      'landscape', 'close-up', 'wide shot', 'bright', 'dark',
      'colorful', 'monochrome', 'vintage', 'modern',
    ];
    
    for (final tag in tagWords) {
      if (text.toLowerCase().contains(tag)) {
        tags.add(tag);
      }
    }
    
    return tags.take(4).toList(); // Limit to top 4
  }
}

/// Result class for vision analysis
class VisionAnalysisResult {
  final String description;
  final double confidence;
  final List<String> detectedObjects;
  final List<String> colors;
  final List<String> tags;
  final Map<String, dynamic> rawResponse;
  final bool isMockData;

  const VisionAnalysisResult({
    required this.description,
    required this.confidence,
    required this.detectedObjects,
    required this.colors,
    required this.tags,
    required this.rawResponse,
    this.isMockData = false,
  });
  
  /// Creates a mock result for testing purposes
  factory VisionAnalysisResult.mock() {
    return VisionAnalysisResult(
      description: "⚠️ MOCK DATA: This is demonstration data only. The image shows a beautiful landscape with mountains in the background and a clear blue sky. There are trees in the foreground and the lighting suggests it was taken during golden hour. (Real AI analysis was not available - check console logs for details)",
      confidence: 0.85,
      detectedObjects: const ['mountain', 'tree', 'sky', 'landscape'],
      colors: const ['blue', 'green', 'brown'],
      tags: const ['outdoor', 'nature', 'landscape', 'bright'],
      rawResponse: {
        'mock': true,
        'reason': 'API unavailable',
        'timestamp': DateTime.now().toIso8601String()
      },
      isMockData: true,
    );
  }
}

/// Custom exception for API-related errors
class ApiException implements Exception {
  final String message;
  
  const ApiException(this.message);
  
  @override
  String toString() => 'ApiException: $message';
}
