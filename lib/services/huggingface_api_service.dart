import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'package:http/http.dart' as http;
import 'package:flutter/foundation.dart';
import '../config/api_config.dart';

/// Service for interacting with Hugging Face InternVL API
/// 
/// This service handles image analysis using the InternVL model hosted on
/// Hugging Face Spaces. It converts images to base64 and sends them to the
/// Gradio API endpoint for analysis.
class HuggingFaceApiService {
  /// Current retry attempt counter
  int _retryCount = 0;
  
  /// Analyzes an image using the InternVL model
  ///
  /// [imageFile] can be either a File (mobile/desktop) or Uint8List (web)
  /// [prompt] is the question or instruction for the model (optional)
  /// [analysisType] determines the type of analysis to perform
  ///
  /// Returns a [VisionAnalysisResult] containing the analysis results
  Future<VisionAnalysisResult> analyzeImage({
    required dynamic imageFile,
    String? prompt,
    String analysisType = 'general',
  }) async {
    _retryCount = 0;
    return _analyzeImageWithRetry(imageFile, prompt, analysisType);
  }

  /// Internal method that handles retries
  Future<VisionAnalysisResult> _analyzeImageWithRetry(
    dynamic imageFile,
    String? prompt,
    String analysisType,
  ) async {
    try {
      // Use provided prompt or default based on analysis type
      final effectivePrompt = prompt ?? ApiConfig.defaultPrompts[analysisType] ?? ApiConfig.defaultPrompts['general']!;

      // Convert image to base64
      String base64Image = await _convertImageToBase64(imageFile);

      // Prepare the request payload
      final payload = {
        "data": [
          base64Image,
          effectivePrompt,
          ApiConfig.defaultModelParams,
        ],
        "fn_index": 0, // Usually 0 for the main predict function
      };

      // Try each endpoint until one works
      for (final baseUrl in ApiConfig.alternativeEndpoints) {
        try {
          final response = await http.post(
            Uri.parse('$baseUrl${ApiConfig.huggingFaceApiEndpoint}'),
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json',
            },
            body: jsonEncode(payload),
          ).timeout(ApiConfig.apiTimeout);

          if (response.statusCode == 200) {
            final responseData = jsonDecode(response.body);
            return _parseResponse(responseData);
          } else if (response.statusCode == 503) {
            // Service temporarily unavailable, try next endpoint
            continue;
          } else {
            throw ApiException(
              'API request failed with status ${response.statusCode}: ${response.body}',
            );
          }
        } on SocketException {
          // Network error, try next endpoint
          continue;
        } on http.ClientException {
          // HTTP client error, try next endpoint
          continue;
        }
      }

      // If all endpoints failed, throw an exception
      throw ApiException('All API endpoints are currently unavailable');

    } catch (e) {
      if (e is ApiException) {
        // Check if we should retry
        if (ApiConfig.enableRetryOnFailure && _retryCount < ApiConfig.maxRetryAttempts) {
          _retryCount++;
          await Future.delayed(Duration(seconds: _retryCount * 2)); // Exponential backoff
          return _analyzeImageWithRetry(imageFile, prompt, analysisType);
        }

        // If retries exhausted or fallback enabled, return mock data
        if (ApiConfig.enableFallbackToMockData) {
          return VisionAnalysisResult.mock();
        }

        rethrow;
      }
      throw ApiException('Failed to analyze image: $e');
    }
  }
  
  /// Converts image file to base64 string
  Future<String> _convertImageToBase64(dynamic imageFile) async {
    try {
      Uint8List bytes;
      
      if (kIsWeb) {
        // Web platform - imageFile is Uint8List
        bytes = imageFile as Uint8List;
      } else {
        // Mobile/Desktop platform - imageFile is File
        final file = imageFile as File;
        bytes = await file.readAsBytes();
      }
      
      // Convert to base64 with data URL prefix
      final base64String = base64Encode(bytes);
      return 'data:image/jpeg;base64,$base64String';
    } catch (e) {
      throw ApiException('Failed to convert image to base64: $e');
    }
  }
  
  /// Parses the API response into a structured result
  VisionAnalysisResult _parseResponse(Map<String, dynamic> responseData) {
    try {
      // The response structure may vary, but typically contains a 'data' field
      final data = responseData['data'];
      
      if (data is List && data.isNotEmpty) {
        final analysisText = data[0] as String? ?? 'No analysis available';
        
        return VisionAnalysisResult(
          description: analysisText,
          confidence: _extractConfidence(analysisText),
          detectedObjects: _extractObjects(analysisText),
          colors: _extractColors(analysisText),
          tags: _extractTags(analysisText),
          rawResponse: responseData,
        );
      } else {
        throw ApiException('Unexpected response format');
      }
    } catch (e) {
      throw ApiException('Failed to parse API response: $e');
    }
  }
  
  /// Extracts confidence score from analysis text (basic implementation)
  double _extractConfidence(String text) {
    // This is a simple heuristic - in a real implementation,
    // you might use more sophisticated text analysis
    if (text.toLowerCase().contains('clearly') || 
        text.toLowerCase().contains('definitely')) {
      return 0.9;
    } else if (text.toLowerCase().contains('appears') || 
               text.toLowerCase().contains('seems')) {
      return 0.7;
    } else if (text.toLowerCase().contains('possibly') || 
               text.toLowerCase().contains('might')) {
      return 0.5;
    }
    return 0.8; // Default confidence
  }
  
  /// Extracts detected objects from analysis text
  List<String> _extractObjects(String text) {
    final objects = <String>[];
    final commonObjects = [
      'person', 'people', 'man', 'woman', 'child', 'baby',
      'car', 'truck', 'bus', 'motorcycle', 'bicycle',
      'dog', 'cat', 'bird', 'horse', 'cow', 'sheep',
      'tree', 'flower', 'grass', 'mountain', 'sky', 'cloud',
      'building', 'house', 'road', 'bridge', 'water', 'ocean',
      'food', 'plate', 'cup', 'bottle', 'chair', 'table',
    ];
    
    for (final object in commonObjects) {
      if (text.toLowerCase().contains(object)) {
        objects.add(object);
      }
    }
    
    return objects.take(5).toList(); // Limit to top 5
  }
  
  /// Extracts color information from analysis text
  List<String> _extractColors(String text) {
    final colors = <String>[];
    final colorWords = [
      'red', 'blue', 'green', 'yellow', 'orange', 'purple',
      'pink', 'brown', 'black', 'white', 'gray', 'grey',
    ];
    
    for (final color in colorWords) {
      if (text.toLowerCase().contains(color)) {
        colors.add(color);
      }
    }
    
    return colors.take(3).toList(); // Limit to top 3
  }
  
  /// Extracts relevant tags from analysis text
  List<String> _extractTags(String text) {
    final tags = <String>[];
    final tagWords = [
      'outdoor', 'indoor', 'nature', 'urban', 'portrait',
      'landscape', 'close-up', 'wide shot', 'bright', 'dark',
      'colorful', 'monochrome', 'vintage', 'modern',
    ];
    
    for (final tag in tagWords) {
      if (text.toLowerCase().contains(tag)) {
        tags.add(tag);
      }
    }
    
    return tags.take(4).toList(); // Limit to top 4
  }
}

/// Result class for vision analysis
class VisionAnalysisResult {
  final String description;
  final double confidence;
  final List<String> detectedObjects;
  final List<String> colors;
  final List<String> tags;
  final Map<String, dynamic> rawResponse;
  
  const VisionAnalysisResult({
    required this.description,
    required this.confidence,
    required this.detectedObjects,
    required this.colors,
    required this.tags,
    required this.rawResponse,
  });
  
  /// Creates a mock result for testing purposes
  factory VisionAnalysisResult.mock() {
    return const VisionAnalysisResult(
      description: "This image shows a beautiful landscape with mountains in the background and a clear blue sky. There are trees in the foreground and the lighting suggests it was taken during golden hour.",
      confidence: 0.85,
      detectedObjects: ['mountain', 'tree', 'sky', 'landscape'],
      colors: ['blue', 'green', 'brown'],
      tags: ['outdoor', 'nature', 'landscape', 'bright'],
      rawResponse: {},
    );
  }
}

/// Custom exception for API-related errors
class ApiException implements Exception {
  final String message;
  
  const ApiException(this.message);
  
  @override
  String toString() => 'ApiException: $message';
}
