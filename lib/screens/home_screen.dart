import 'package:flutter/material.dart';
import '../theme/app_colors.dart';
import '../widgets/custom_widgets.dart';

/// Home screen showcasing the PowerSights dark theme
///
/// This screen demonstrates the proper usage of all theme elements
/// including colors, typography, and custom widgets.
class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  int _counter = 0;
  bool _showAlert = true;
  final List<String> _selectedChips = [];

  void _incrementCounter() {
    setState(() {
      _counter++;
    });
  }

  void _showSnackBar() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('This is a PowerSights themed snackbar!'),
        action: SnackBarAction(
          label: 'UNDO',
          onPressed: () {
            setState(() {
              _counter = 0;
            });
          },
        ),
      ),
    );
  }

  void _showDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('PowerSights Dialog'),
        content: const Text(
          'This dialog demonstrates the dark theme with gold accents. '
          'Notice how the colors maintain excellent contrast and readability.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('CANCEL'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('CONFIRM'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('PowerSights'),
        actions: [
          IconButton(
            onPressed: _showDialog,
            icon: const Icon(Icons.info_outline),
            tooltip: 'Show Dialog',
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Welcome section
            PowerCard(
              elevated: true,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.wb_sunny, color: AppColors.gold, size: 32),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Welcome to PowerSights',
                              style: Theme.of(context).textTheme.headlineSmall,
                            ),
                            const SizedBox(height: 4),
                            Text(
                              'Dark theme with gold accents',
                              style: Theme.of(context).textTheme.bodyMedium
                                  ?.copyWith(color: AppColors.secondaryText),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'This app demonstrates a beautiful dark mode theme using the specified color palette. '
                    'Every UI element follows the design guidelines for optimal contrast and readability.',
                    style: Theme.of(context).textTheme.bodyLarge,
                  ),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // Alert section
            if (_showAlert) ...[
              PowerAlert(
                title: 'System Alert',
                message:
                    'This is an example alert using the red accent color for important notifications.',
                icon: Icons.warning,
                onDismiss: () {
                  setState(() {
                    _showAlert = false;
                  });
                },
              ),
              const SizedBox(height: 16),
            ],

            // Counter section
            PowerCard(
              child: Column(
                children: [
                  Text(
                    'Counter Demo',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'You have pushed the button this many times:',
                    style: Theme.of(context).textTheme.bodyLarge,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '$_counter',
                    style: Theme.of(context).textTheme.displayMedium?.copyWith(
                      color: AppColors.gold,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      PowerButton(
                        text: 'Increment',
                        icon: Icons.add,
                        onPressed: _incrementCounter,
                      ),
                      PowerButton(
                        text: 'Show Snackbar',
                        icon: Icons.message,
                        outlined: true,
                        onPressed: _showSnackBar,
                      ),
                    ],
                  ),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // Form section
            PowerCard(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Form Elements',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: 16),
                  const PowerTextField(
                    label: 'Email',
                    hint: 'Enter your email address',
                    prefixIcon: Icons.email,
                    keyboardType: TextInputType.emailAddress,
                  ),
                  const SizedBox(height: 16),
                  const PowerTextField(
                    label: 'Password',
                    hint: 'Enter your password',
                    prefixIcon: Icons.lock,
                    suffixIcon: Icons.visibility,
                    obscureText: true,
                  ),
                  const SizedBox(height: 16),
                  const PowerTextField(
                    label: 'Message',
                    hint: 'Enter your message here...',
                    maxLines: 3,
                  ),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // Chips section
            PowerCard(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Filter Chips',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: 16),
                  Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children:
                        [
                              'Analytics',
                              'Reports',
                              'Dashboard',
                              'Settings',
                              'Profile',
                            ]
                            .map(
                              (chip) => PowerChip(
                                label: chip,
                                selected: _selectedChips.contains(chip),
                                icon: Icons.label,
                                onTap: () {
                                  setState(() {
                                    if (_selectedChips.contains(chip)) {
                                      _selectedChips.remove(chip);
                                    } else {
                                      _selectedChips.add(chip);
                                    }
                                  });
                                },
                              ),
                            )
                            .toList(),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // Progress indicators
            PowerCard(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Progress Indicators',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: 16),
                  const Text('Linear Progress:'),
                  const SizedBox(height: 8),
                  const PowerProgressIndicator(value: 0.7, linear: true),
                  const SizedBox(height: 16),
                  const Text('Circular Progress:'),
                  const SizedBox(height: 8),
                  const Row(
                    children: [
                      PowerProgressIndicator(value: 0.5),
                      SizedBox(width: 16),
                      PowerProgressIndicator(), // Indeterminate
                    ],
                  ),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // Divider demo
            const PowerDivider(height: 32),

            // Color palette showcase
            PowerCard(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Color Palette',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: 16),
                  _buildColorSwatch('Background', AppColors.background),
                  _buildColorSwatch('Primary Text', AppColors.primaryText),
                  _buildColorSwatch('Gold', AppColors.gold),
                  _buildColorSwatch('Dark Gold', AppColors.darkGold),
                  _buildColorSwatch('Red', AppColors.red),
                  _buildColorSwatch('Brown', AppColors.brown),
                ],
              ),
            ),

            const SizedBox(height: 32),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _incrementCounter,
        tooltip: 'Increment Counter',
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildColorSwatch(String name, Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              color: color,
              borderRadius: BorderRadius.circular(4),
              border: Border.all(color: AppColors.brown, width: 1),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(name, style: Theme.of(context).textTheme.bodyMedium),
          ),
          Text(
            '#${color.toARGB32().toRadixString(16).substring(2).toUpperCase()}',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              fontFamily: 'monospace',
              color: AppColors.secondaryText,
            ),
          ),
        ],
      ),
    );
  }
}
