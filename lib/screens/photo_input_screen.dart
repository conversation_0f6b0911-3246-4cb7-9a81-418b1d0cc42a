import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import 'package:flutter/foundation.dart'; // For kIsWeb
import 'package:google_fonts/google_fonts.dart';

import '../widgets/custom_widgets.dart';
import '../theme/app_colors.dart';
import 'photo_confirm_screen.dart';

/// Enhanced screen allowing the user to take a photo with the camera or
/// choose an existing image from the gallery with beautiful animations and modern UI.
///
/// Once an image is selected it navigates to [PhotoConfirmScreen]
/// where the user can either retake the photo or confirm and
/// continue to the results screen.
class PhotoInputScreen extends StatefulWidget {
  const PhotoInputScreen({super.key});

  @override
  State<PhotoInputScreen> createState() => _PhotoInputScreenState();
}

class _PhotoInputScreenState extends State<PhotoInputScreen>
    with TickerProviderStateMixin {
  final ImagePicker _picker = ImagePicker();
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _scaleAnimation;

  bool _isLoading = false;

  @override
  void initState() {
    super.initState();

    // Initialize animations
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _slideController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );

    _slideAnimation =
        Tween<Offset>(begin: const Offset(0, 0.5), end: Offset.zero).animate(
          CurvedAnimation(parent: _slideController, curve: Curves.elasticOut),
        );

    _scaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(parent: _slideController, curve: Curves.elasticOut),
    );

    // Start animations
    _fadeController.forward();
    _slideController.forward();
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  Future<void> _pick(ImageSource source) async {
    if (_isLoading) return;

    setState(() => _isLoading = true);

    try {
      final XFile? picked = await _picker.pickImage(
        source: source,
        maxWidth: 1800,
        maxHeight: 1800,
        imageQuality: 85,
      );

      if (picked != null && context.mounted) {
        // Add a brief delay for better UX
        await Future.delayed(const Duration(milliseconds: 300));

        // On web, pass bytes; on other platforms, pass File
        if (kIsWeb) {
          final bytes = await picked.readAsBytes();
          Navigator.push(
            context,
            PageRouteBuilder(
              pageBuilder: (context, animation, secondaryAnimation) =>
                  PhotoConfirmScreen(imageFile: bytes),
              transitionsBuilder:
                  (context, animation, secondaryAnimation, child) {
                    const begin = Offset(1.0, 0.0);
                    const end = Offset.zero;
                    const curve = Curves.easeInOutCubic;

                    var tween = Tween(
                      begin: begin,
                      end: end,
                    ).chain(CurveTween(curve: curve));

                    return SlideTransition(
                      position: animation.drive(tween),
                      child: FadeTransition(opacity: animation, child: child),
                    );
                  },
              transitionDuration: const Duration(milliseconds: 400),
            ),
          );
        } else {
          Navigator.push(
            context,
            PageRouteBuilder(
              pageBuilder: (context, animation, secondaryAnimation) =>
                  PhotoConfirmScreen(imageFile: File(picked.path)),
              transitionsBuilder:
                  (context, animation, secondaryAnimation, child) {
                    const begin = Offset(1.0, 0.0);
                    const end = Offset.zero;
                    const curve = Curves.easeInOutCubic;

                    var tween = Tween(
                      begin: begin,
                      end: end,
                    ).chain(CurveTween(curve: curve));

                    return SlideTransition(
                      position: animation.drive(tween),
                      child: FadeTransition(opacity: animation, child: child),
                    );
                  },
              transitionDuration: const Duration(milliseconds: 400),
            ),
          );
        }
      }
    } catch (e) {
      // If the user cancels or an error occurs, show a subtle feedback
      debugPrint('Image pick error: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Unable to access image. Please try again.',
              style: GoogleFonts.inter(color: AppColors.primaryText),
            ),
            backgroundColor: AppColors.red.withOpacity(0.9),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            margin: const EdgeInsets.all(16),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 700;

    return Scaffold(
      backgroundColor: AppColors.background,
      body: Container(
        decoration: BoxDecoration(
          gradient: RadialGradient(
            center: Alignment.topRight,
            radius: 1.2,
            colors: [
              AppColors.gold.withOpacity(0.03),
              AppColors.background,
              AppColors.background,
            ],
          ),
        ),
        child: SafeArea(
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: Column(
              children: [
                // Enhanced App Bar
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 16,
                  ),
                  child: Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: AppColors.gold.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Icon(
                          Icons.photo_camera_outlined,
                          color: AppColors.gold,
                          size: 28,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'New Analysis',
                              style: GoogleFonts.inter(
                                fontSize: 24,
                                fontWeight: FontWeight.w700,
                                color: AppColors.primaryText,
                                letterSpacing: -0.5,
                              ),
                            ),
                            Text(
                              'Capture or select an image',
                              style: GoogleFonts.inter(
                                fontSize: 14,
                                color: AppColors.secondaryText,
                                letterSpacing: 0.1,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),

                // Main content
                Expanded(
                  child: Center(
                    child: ConstrainedBox(
                      constraints: const BoxConstraints(maxWidth: 450),
                      child: Padding(
                        padding: EdgeInsets.symmetric(
                          horizontal: 24,
                          vertical: isShortScreen ? 12 : 32,
                        ),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            // Animated instruction card
                            SlideTransition(
                              position: _slideAnimation,
                              child: ScaleTransition(
                                scale: _scaleAnimation,
                                child: PowerCard(
                                  elevated: true,
                                  child: Column(
                                    children: [
                                      // Animated camera icon with pulsing effect
                                      TweenAnimationBuilder<double>(
                                        tween: Tween(begin: 0.8, end: 1.0),
                                        duration: const Duration(seconds: 2),
                                        curve: Curves.easeInOut,
                                        builder: (context, scale, child) {
                                          return Transform.scale(
                                            scale: scale,
                                            child: Container(
                                              padding: const EdgeInsets.all(24),
                                              decoration: BoxDecoration(
                                                color: AppColors.gold
                                                    .withOpacity(0.1),
                                                borderRadius:
                                                    BorderRadius.circular(20),
                                                boxShadow: [
                                                  BoxShadow(
                                                    color: AppColors.gold
                                                        .withOpacity(0.2),
                                                    blurRadius: 20,
                                                    spreadRadius: 2,
                                                  ),
                                                ],
                                              ),
                                              child: const Icon(
                                                Icons
                                                    .photo_camera_back_outlined,
                                                color: AppColors.gold,
                                                size: 72,
                                              ),
                                            ),
                                          );
                                        },
                                        onEnd: () {
                                          // Restart the animation
                                          Future.delayed(
                                            const Duration(milliseconds: 500),
                                            () {
                                              if (mounted) {
                                                setState(() {});
                                              }
                                            },
                                          );
                                        },
                                      ),

                                      const SizedBox(height: 24),

                                      Text(
                                        'Add a photo',
                                        style: GoogleFonts.inter(
                                          fontSize: 28,
                                          fontWeight: FontWeight.w700,
                                          color: AppColors.primaryText,
                                          letterSpacing: -0.5,
                                        ),
                                      ),

                                      const SizedBox(height: 12),

                                      Text(
                                        'Choose an existing image or take a new photo to begin your analysis',
                                        style: GoogleFonts.inter(
                                          fontSize: 16,
                                          color: AppColors.secondaryText,
                                          height: 1.4,
                                          letterSpacing: 0.1,
                                        ),
                                        textAlign: TextAlign.center,
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),

                            SizedBox(height: isShortScreen ? 24 : 40),

                            // Animated buttons with staggered entrance
                            AnimatedBuilder(
                              animation: _slideController,
                              builder: (context, child) {
                                return Column(
                                  children: [
                                    // Take Photo button
                                    Transform.translate(
                                      offset: Offset(
                                        0,
                                        (1 - _slideController.value) * 50,
                                      ),
                                      child: Opacity(
                                        opacity: _slideController.value,
                                        child: PowerButton(
                                          text: 'Take Photo',
                                          icon: Icons.camera_alt_rounded,
                                          loading: _isLoading,
                                          onPressed: () =>
                                              _pick(ImageSource.camera),
                                        ),
                                      ),
                                    ),

                                    const SizedBox(height: 16),

                                    // Choose from Gallery button with slight delay
                                    Transform.translate(
                                      offset: Offset(
                                        0,
                                        (1 -
                                                (_slideController.value - 0.1)
                                                    .clamp(0.0, 1.0)) *
                                            50,
                                      ),
                                      child: Opacity(
                                        opacity: (_slideController.value - 0.1)
                                            .clamp(0.0, 1.0),
                                        child: PowerButton(
                                          text: 'Choose from Gallery',
                                          icon: Icons.photo_library_rounded,
                                          outlined: true,
                                          loading: _isLoading,
                                          onPressed: () =>
                                              _pick(ImageSource.gallery),
                                        ),
                                      ),
                                    ),
                                  ],
                                );
                              },
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),

                // Subtle footer
                Padding(
                  padding: const EdgeInsets.all(24),
                  child: Text(
                    'Powered by PowerSights AI',
                    style: GoogleFonts.inter(
                      fontSize: 12,
                      color: AppColors.hintText,
                      letterSpacing: 0.5,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
