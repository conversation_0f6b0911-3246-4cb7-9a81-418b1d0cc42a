import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart'; // For kIsWeb
import 'package:google_fonts/google_fonts.dart';

import '../widgets/custom_widgets.dart';
import '../theme/app_colors.dart';
import 'photo_input_screen.dart';

/// Enhanced screen that displays the selected image along with analysis results
/// featuring beautiful animations and modern UI design.
class PhotoResultScreen extends StatefulWidget {
  final dynamic imageFile; // File or bytes

  const PhotoResultScreen({super.key, required this.imageFile});

  @override
  State<PhotoResultScreen> createState() => _PhotoResultScreenState();
}

class _PhotoResultScreenState extends State<PhotoResultScreen>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late AnimationController _imageController;
  late AnimationController _resultsController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _imageScaleAnimation;
  late Animation<double> _resultsOpacityAnimation;
  late Animation<double> _resultsScaleAnimation;

  bool _isAnalyzing = true;

  @override
  void initState() {
    super.initState();

    // Initialize animations
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _slideController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _imageController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _resultsController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );

    _slideAnimation =
        Tween<Offset>(begin: const Offset(0, 0.5), end: Offset.zero).animate(
          CurvedAnimation(parent: _slideController, curve: Curves.elasticOut),
        );

    _imageScaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(parent: _imageController, curve: Curves.elasticOut),
    );

    _resultsOpacityAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _resultsController, curve: Curves.easeIn),
    );

    _resultsScaleAnimation = Tween<double>(begin: 0.9, end: 1.0).animate(
      CurvedAnimation(parent: _resultsController, curve: Curves.elasticOut),
    );

    // Start animations with staggered timing
    _fadeController.forward();
    Future.delayed(const Duration(milliseconds: 200), () {
      if (mounted) {
        _imageController.forward();
      }
    });

    // Simulate analysis process
    Future.delayed(const Duration(milliseconds: 1500), () {
      if (mounted) {
        setState(() {
          _isAnalyzing = false;
        });
        _resultsController.forward();
        _slideController.forward();
      }
    });
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _slideController.dispose();
    _imageController.dispose();
    _resultsController.dispose();
    super.dispose();
  }

  void _handleNewAnalysis() {
    Navigator.pushAndRemoveUntil(
      context,
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) =>
            const PhotoInputScreen(),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          const begin = Offset(-1.0, 0.0);
          const end = Offset.zero;
          const curve = Curves.easeInOutCubic;

          var tween = Tween(
            begin: begin,
            end: end,
          ).chain(CurveTween(curve: curve));

          return SlideTransition(
            position: animation.drive(tween),
            child: FadeTransition(opacity: animation, child: child),
          );
        },
        transitionDuration: const Duration(milliseconds: 400),
      ),
      (route) => false,
    );
  }

  Widget _buildAnalysisResults() {
    return AnimatedBuilder(
      animation: _resultsController,
      builder: (context, child) {
        return Transform.scale(
          scale: _resultsScaleAnimation.value,
          child: Opacity(
            opacity: _resultsOpacityAnimation.value,
            child: SingleChildScrollView(
              physics: const BouncingScrollPhysics(),
              child: Column(
                children: [
                  // Main results card
                  PowerCard(
                    elevated: true,
                    child: Column(
                      children: [
                        // Analytics icon with glow effect
                        Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: AppColors.gold.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(16),
                            boxShadow: [
                              BoxShadow(
                                color: AppColors.gold.withOpacity(0.3),
                                blurRadius: 15,
                                spreadRadius: 2,
                              ),
                            ],
                          ),
                          child: const Icon(
                            Icons.analytics_outlined,
                            color: AppColors.gold,
                            size: 40,
                          ),
                        ),

                        const SizedBox(height: 20),

                        Text(
                          'Analysis Complete',
                          style: GoogleFonts.inter(
                            fontSize: 24,
                            fontWeight: FontWeight.w700,
                            color: AppColors.primaryText,
                            letterSpacing: -0.5,
                          ),
                        ),

                        const SizedBox(height: 12),

                        Text(
                          'Your image has been successfully analyzed. Here are the key insights:',
                          style: GoogleFonts.inter(
                            fontSize: 15,
                            color: AppColors.secondaryText,
                            height: 1.4,
                            letterSpacing: 0.1,
                          ),
                          textAlign: TextAlign.center,
                        ),

                        const SizedBox(height: 24),

                        // Placeholder results with modern styling
                        Container(
                          padding: const EdgeInsets.all(20),
                          decoration: BoxDecoration(
                            color: AppColors.surface.withOpacity(0.5),
                            borderRadius: BorderRadius.circular(16),
                            border: Border.all(
                              color: AppColors.gold.withOpacity(0.2),
                              width: 1,
                            ),
                          ),
                          child: Column(
                            children: [
                              _buildResultItem(
                                'Confidence Score',
                                '94.2%',
                                Icons.verified_outlined,
                                AppColors.success,
                              ),
                              const SizedBox(height: 16),
                              _buildResultItem(
                                'Processing Time',
                                '1.8 seconds',
                                Icons.timer_outlined,
                                AppColors.gold,
                              ),
                              const SizedBox(height: 16),
                              _buildResultItem(
                                'Analysis Type',
                                'Computer Vision',
                                Icons.visibility_outlined,
                                AppColors.info,
                              ),
                            ],
                          ),
                        ),

                        const SizedBox(height: 20),

                        // Note about API integration
                        Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: AppColors.brown.withOpacity(0.3),
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: AppColors.brown.withOpacity(0.5),
                              width: 1,
                            ),
                          ),
                          child: Row(
                            children: [
                              Icon(
                                Icons.info_outline_rounded,
                                color: AppColors.gold.withOpacity(0.8),
                                size: 20,
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: Text(
                                  'API integration pending. Real analysis results will be displayed here once available.',
                                  style: GoogleFonts.inter(
                                    fontSize: 13,
                                    color: AppColors.secondaryText,
                                    height: 1.3,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 24),

                  // Action button
                  SlideTransition(
                    position: _slideAnimation,
                    child: PowerButton(
                      text: 'New Analysis',
                      icon: Icons.add_a_photo_rounded,
                      onPressed: _handleNewAnalysis,
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildResultItem(
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, color: color, size: 20),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: GoogleFonts.inter(
                  fontSize: 14,
                  color: AppColors.secondaryText,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 2),
              Text(
                value,
                style: GoogleFonts.inter(
                  fontSize: 16,
                  color: AppColors.primaryText,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildAnalyzingIndicator() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Animated analyzing indicator
          TweenAnimationBuilder<double>(
            tween: Tween(begin: 0.0, end: 1.0),
            duration: const Duration(seconds: 2),
            curve: Curves.easeInOut,
            builder: (context, value, child) {
              return Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: AppColors.gold.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(40),
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.gold.withOpacity(value * 0.3),
                      blurRadius: 20,
                      spreadRadius: value * 5,
                    ),
                  ],
                ),
                child: Center(
                  child: CircularProgressIndicator(
                    strokeWidth: 3,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      AppColors.gold.withOpacity(0.8),
                    ),
                  ),
                ),
              );
            },
            onEnd: () {
              // Restart the animation
              Future.delayed(const Duration(milliseconds: 100), () {
                if (mounted && _isAnalyzing) {
                  setState(() {});
                }
              });
            },
          ),

          const SizedBox(height: 24),

          Text(
            'Analyzing Image...',
            style: GoogleFonts.inter(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: AppColors.primaryText,
              letterSpacing: 0.1,
            ),
          ),

          const SizedBox(height: 8),

          Text(
            'Please wait while we process your image',
            style: GoogleFonts.inter(
              fontSize: 14,
              color: AppColors.secondaryText,
              letterSpacing: 0.1,
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 700;

    return Scaffold(
      backgroundColor: AppColors.background,
      body: Container(
        decoration: BoxDecoration(
          gradient: RadialGradient(
            center: Alignment.bottomRight,
            radius: 1.2,
            colors: [AppColors.gold.withOpacity(0.02), AppColors.background],
          ),
        ),
        child: SafeArea(
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: Column(
              children: [
                // Enhanced App Bar
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 16,
                  ),
                  child: Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: AppColors.gold.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Icon(
                          Icons.analytics_outlined,
                          color: AppColors.gold,
                          size: 28,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Analysis Results',
                              style: GoogleFonts.inter(
                                fontSize: 24,
                                fontWeight: FontWeight.w700,
                                color: AppColors.primaryText,
                                letterSpacing: -0.5,
                              ),
                            ),
                            Text(
                              _isAnalyzing
                                  ? 'Processing...'
                                  : 'Analysis complete',
                              style: GoogleFonts.inter(
                                fontSize: 14,
                                color: _isAnalyzing
                                    ? AppColors.gold
                                    : AppColors.success,
                                letterSpacing: 0.1,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),

                // Main content
                Expanded(
                  child: Padding(
                    padding: EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: isShortScreen ? 12 : 20,
                    ),
                    child: Column(
                      children: [
                        // Enhanced image preview
                        Container(
                          height: isShortScreen ? 200 : 280,
                          child: AnimatedBuilder(
                            animation: _imageController,
                            builder: (context, child) {
                              return Transform.scale(
                                scale: _imageScaleAnimation.value,
                                child: Hero(
                                  tag: 'selected_image',
                                  child: Container(
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(20),
                                      boxShadow: [
                                        BoxShadow(
                                          color: AppColors.gold.withOpacity(
                                            0.15,
                                          ),
                                          blurRadius: 25,
                                          spreadRadius: 5,
                                          offset: const Offset(0, 8),
                                        ),
                                        BoxShadow(
                                          color: Colors.black.withOpacity(0.3),
                                          blurRadius: 15,
                                          offset: const Offset(0, 4),
                                        ),
                                      ],
                                    ),
                                    child: ClipRRect(
                                      borderRadius: BorderRadius.circular(20),
                                      child: Container(
                                        decoration: BoxDecoration(
                                          border: Border.all(
                                            color: AppColors.gold.withOpacity(
                                              0.3,
                                            ),
                                            width: 2,
                                          ),
                                          borderRadius: BorderRadius.circular(
                                            20,
                                          ),
                                        ),
                                        child: ClipRRect(
                                          borderRadius: BorderRadius.circular(
                                            18,
                                          ),
                                          child: kIsWeb
                                              ? Image.memory(
                                                  widget.imageFile,
                                                  fit: BoxFit.cover,
                                                  width: double.infinity,
                                                )
                                              : Image.file(
                                                  widget.imageFile,
                                                  fit: BoxFit.cover,
                                                  width: double.infinity,
                                                ),
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              );
                            },
                          ),
                        ),

                        SizedBox(height: isShortScreen ? 20 : 32),

                        // Results or analyzing indicator
                        Expanded(
                          child: _isAnalyzing
                              ? _buildAnalyzingIndicator()
                              : _buildAnalysisResults(),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
