import 'package:flutter/material.dart';

/// PowerSights App Color Palette
/// 
/// This class defines the complete color scheme for the PowerSights app
/// following the dark mode theme specifications with optimal contrast ratios.
class AppColors {
  // Private constructor to prevent instantiation
  AppColors._();

  // Primary Colors
  /// Main background color - Pure black for maximum contrast
  static const Color background = Color(0xFF000000);
  
  /// Primary text color - Pure white for optimal readability on black
  static const Color primaryText = Color(0xFFFFFFFF);
  
  /// Gold color for highlights, icons, and primary buttons
  static const Color gold = Color(0xFFE5B611);
  
  // Accent Colors
  /// Red color for alerts, errors, and active states
  static const Color red = Color(0xFF921A28);
  
  /// Brown color for subtle dividers and footers
  static const Color brown = Color(0xFF3F1F14);
  
  /// Dark gold for secondary highlights and accents
  static const Color darkGold = Color(0xFFD1A33C);
  
  // Additional utility colors derived from the palette
  /// Light gold for text that needs to stand out but not as much as primary gold
  static const Color lightGold = Color(0xFFF5E003);
  
  /// Semi-transparent white for secondary text
  static const Color secondaryText = Color(0xFFE0E0E0);
  
  /// Very subtle text for hints and disabled states
  static const Color hintText = Color(0xFF9E9E9E);
  
  /// Surface color for cards and elevated elements
  static const Color surface = Color(0xFF121212);
  
  /// Slightly lighter surface for elevated cards
  static const Color surfaceElevated = Color(0xFF1E1E1E);
  
  // State colors
  /// Success color derived from gold palette
  static const Color success = Color(0xFF4CAF50);
  
  /// Warning color using our gold
  static const Color warning = gold;
  
  /// Error color using our red
  static const Color error = red;
  
  /// Info color using a blue that complements our palette
  static const Color info = Color(0xFF2196F3);
  
  // Transparent overlays
  /// Semi-transparent black for overlays
  static const Color overlay = Color(0x80000000);
  
  /// Semi-transparent gold for hover states
  static const Color goldOverlay = Color(0x20E5B611);
  
  /// Semi-transparent red for error overlays
  static const Color redOverlay = Color(0x20921A28);

  // Material Color Swatches for advanced theming
  static const MaterialColor goldSwatch = MaterialColor(
    0xFFE5B611,
    <int, Color>{
      50: Color(0xFFFDF8E1),
      100: Color(0xFFFAEDB3),
      200: Color(0xFFF7E082),
      300: Color(0xFFF4D350),
      400: Color(0xFFF1C92B),
      500: Color(0xFFE5B611), // Primary gold
      600: Color(0xFFD1A33C), // Dark gold
      700: Color(0xFFB8941F),
      800: Color(0xFFA08419),
      900: Color(0xFF7A6610),
    },
  );

  static const MaterialColor redSwatch = MaterialColor(
    0xFF921A28,
    <int, Color>{
      50: Color(0xFFF3E5E7),
      100: Color(0xFFE1BEC3),
      200: Color(0xFFCD939B),
      300: Color(0xFFB96873),
      400: Color(0xFFAA4755),
      500: Color(0xFF921A28), // Primary red
      600: Color(0xFF8A1725),
      700: Color(0xFF7F1320),
      800: Color(0xFF750F1C),
      900: Color(0xFF640813),
    },
  );
}
