import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'theme/app_theme.dart';
import 'screens/photo_input_screen.dart';

/// PowerSights - Photo Analysis App with Dark Mode Theme
///
/// This app provides a photo analysis feature with a beautiful dark theme
/// using a carefully crafted color palette for optimal user experience.
void main() {
  WidgetsFlutterBinding.ensureInitialized();

  // Set system UI overlay style for consistent theming
  SystemChrome.setSystemUIOverlayStyle(AppTheme.systemUiOverlayStyle);

  runApp(const PowerSightsApp());
}

/// Main application widget
class PowerSightsApp extends StatelessWidget {
  const PowerSightsApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'PowerSights',
      debugShowCheckedModeBanner: false,

      // Use our custom dark theme
      theme: AppTheme.darkTheme,

      // Start with the photo analysis flow
      home: const PhotoInputScreen(),

      // Configure the app for dark mode
      themeMode: ThemeMode.dark,
    );
  }
}
