import 'package:flutter/material.dart';
import '../theme/app_colors.dart';
import 'package:google_fonts/google_fonts.dart';

/// Custom widgets showcasing the PowerSights dark theme
///
/// This file contains reusable widgets that demonstrate the proper
/// usage of the PowerSights color palette and theme system.

/// Enhanced custom card widget with PowerSights styling, animations, and gradients
class PowerCard extends StatefulWidget {
  final Widget child;
  final EdgeInsetsGeometry? padding;
  final VoidCallback? onTap;
  final bool elevated;

  const PowerCard({
    super.key,
    required this.child,
    this.padding,
    this.onTap,
    this.elevated = false,
  });

  @override
  State<PowerCard> createState() => _PowerCardState();
}

class _PowerCardState extends State<PowerCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _glowAnimation;
  bool _isHovered = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(begin: 1.0, end: 0.98).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _glowAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _onTapDown(TapDownDetails details) {
    _animationController.forward();
  }

  void _onTapUp(TapUpDetails details) {
    _animationController.reverse();
    if (widget.onTap != null) {
      widget.onTap!();
    }
  }

  void _onTapCancel() {
    _animationController.reverse();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: MouseRegion(
            onEnter: (_) => setState(() => _isHovered = true),
            onExit: (_) => setState(() => _isHovered = false),
            child: GestureDetector(
              onTapDown: widget.onTap != null ? _onTapDown : null,
              onTapUp: widget.onTap != null ? _onTapUp : null,
              onTapCancel: widget.onTap != null ? _onTapCancel : null,
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 300),
                curve: Curves.easeInOut,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(20),
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      (widget.elevated
                              ? AppColors.surfaceElevated
                              : AppColors.surface)
                          .withOpacity(0.95),
                      (widget.elevated
                              ? AppColors.surfaceElevated
                              : AppColors.surface)
                          .withOpacity(0.85),
                    ],
                  ),
                  border: Border.all(
                    color: AppColors.gold.withOpacity(_isHovered ? 0.8 : 0.5),
                    width: _isHovered ? 2.0 : 1.5,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.gold.withOpacity(
                        _isHovered ? 0.25 : 0.15,
                      ),
                      blurRadius: _isHovered ? 20 : 12,
                      spreadRadius: _isHovered ? 2 : 0,
                      offset: const Offset(0, 4),
                    ),
                    BoxShadow(
                      color: Colors.black.withOpacity(0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(20),
                  child: BackdropFilter(
                    filter: _isHovered
                        ? ColorFilter.mode(
                            AppColors.gold.withOpacity(0.05),
                            BlendMode.overlay,
                          )
                        : ColorFilter.mode(
                            Colors.transparent,
                            BlendMode.multiply,
                          ),
                    child: Padding(
                      padding: widget.padding ?? const EdgeInsets.all(24),
                      child: widget.child,
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

/// Custom alert widget using the red accent color with enhanced animations
class PowerAlert extends StatefulWidget {
  final String title;
  final String message;
  final IconData? icon;
  final VoidCallback? onDismiss;

  const PowerAlert({
    super.key,
    required this.title,
    required this.message,
    this.icon,
    this.onDismiss,
  });

  @override
  State<PowerAlert> createState() => _PowerAlertState();
}

class _PowerAlertState extends State<PowerAlert>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _slideAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );
    _slideAnimation = Tween<double>(
      begin: -100,
      end: 0,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.elasticOut));
    _fadeAnimation = Tween<double>(
      begin: 0,
      end: 1,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeIn));
    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(0, _slideAnimation.value),
          child: Opacity(
            opacity: _fadeAnimation.value,
            child: Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    AppColors.red.withOpacity(0.15),
                    AppColors.red.withOpacity(0.05),
                  ],
                ),
                border: Border.all(
                  color: AppColors.red.withOpacity(0.8),
                  width: 1.5,
                ),
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.red.withOpacity(0.2),
                    blurRadius: 12,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Row(
                children: [
                  if (widget.icon != null) ...[
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: AppColors.red.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(widget.icon, color: AppColors.red, size: 24),
                    ),
                    const SizedBox(width: 16),
                  ],
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          widget.title,
                          style: GoogleFonts.inter(
                            color: AppColors.red,
                            fontWeight: FontWeight.w700,
                            fontSize: 16,
                            letterSpacing: 0.1,
                          ),
                        ),
                        const SizedBox(height: 6),
                        Text(
                          widget.message,
                          style: GoogleFonts.inter(
                            color: AppColors.primaryText,
                            fontSize: 14,
                            height: 1.4,
                          ),
                        ),
                      ],
                    ),
                  ),
                  if (widget.onDismiss != null)
                    Material(
                      color: Colors.transparent,
                      child: InkWell(
                        onTap: widget.onDismiss,
                        borderRadius: BorderRadius.circular(20),
                        child: Padding(
                          padding: const EdgeInsets.all(8),
                          child: Icon(
                            Icons.close_rounded,
                            color: AppColors.red,
                            size: 20,
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}

/// Enhanced button with advanced styling, animations, and haptic feedback
class PowerButton extends StatefulWidget {
  final String text;
  final VoidCallback? onPressed;
  final IconData? icon;
  final bool outlined;
  final bool loading;

  const PowerButton({
    super.key,
    required this.text,
    this.onPressed,
    this.icon,
    this.outlined = false,
    this.loading = false,
  });

  @override
  State<PowerButton> createState() => _PowerButtonState();
}

class _PowerButtonState extends State<PowerButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _shimmerAnimation;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(begin: 1.0, end: 0.96).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _shimmerAnimation = Tween<double>(begin: -2.0, end: 2.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _onTapDown(TapDownDetails details) {
    setState(() => _isPressed = true);
    _animationController.forward();
  }

  void _onTapUp(TapUpDetails details) {
    setState(() => _isPressed = false);
    _animationController.reverse();
    if (widget.onPressed != null && !widget.loading) {
      widget.onPressed!();
    }
  }

  void _onTapCancel() {
    setState(() => _isPressed = false);
    _animationController.reverse();
  }

  @override
  Widget build(BuildContext context) {
    final buttonContent = Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        if (widget.loading)
          SizedBox(
            width: 20,
            height: 20,
            child: CircularProgressIndicator(
              strokeWidth: 2.5,
              valueColor: AlwaysStoppedAnimation<Color>(
                widget.outlined ? AppColors.gold : AppColors.background,
              ),
            ),
          )
        else if (widget.icon != null) ...[
          Icon(widget.icon, size: 22),
          const SizedBox(width: 12),
        ],
        Text(
          widget.text,
          style: GoogleFonts.inter(
            fontWeight: FontWeight.w700,
            fontSize: 16,
            letterSpacing: 0.5,
          ),
        ),
      ],
    );

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: GestureDetector(
            onTapDown: widget.onPressed != null && !widget.loading
                ? _onTapDown
                : null,
            onTapUp: widget.onPressed != null && !widget.loading
                ? _onTapUp
                : null,
            onTapCancel: widget.onPressed != null && !widget.loading
                ? _onTapCancel
                : null,
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              height: 56,
              decoration: BoxDecoration(
                gradient: widget.outlined
                    ? null
                    : LinearGradient(
                        colors: [AppColors.gold, AppColors.darkGold],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                border: widget.outlined
                    ? Border.all(color: AppColors.gold, width: 2)
                    : null,
                borderRadius: BorderRadius.circular(28),
                boxShadow: [
                  if (!widget.outlined)
                    BoxShadow(
                      color: AppColors.gold.withOpacity(_isPressed ? 0.4 : 0.3),
                      blurRadius: _isPressed ? 16 : 12,
                      spreadRadius: _isPressed ? 2 : 0,
                      offset: const Offset(0, 4),
                    ),
                  BoxShadow(
                    color: Colors.black.withOpacity(0.2),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  onTap: widget.onPressed != null && !widget.loading
                      ? widget.onPressed
                      : null,
                  borderRadius: BorderRadius.circular(28),
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: 16,
                    ),
                    child: Center(
                      child: DefaultTextStyle(
                        style: TextStyle(
                          color: widget.outlined
                              ? AppColors.gold
                              : AppColors.background,
                        ),
                        child: buttonContent,
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

/// Custom input field with PowerSights styling
class PowerTextField extends StatelessWidget {
  final String? label;
  final String? hint;
  final TextEditingController? controller;
  final bool obscureText;
  final IconData? prefixIcon;
  final IconData? suffixIcon;
  final VoidCallback? onSuffixIconTap;
  final String? errorText;
  final TextInputType? keyboardType;
  final int? maxLines;

  const PowerTextField({
    super.key,
    this.label,
    this.hint,
    this.controller,
    this.obscureText = false,
    this.prefixIcon,
    this.suffixIcon,
    this.onSuffixIconTap,
    this.errorText,
    this.keyboardType,
    this.maxLines = 1,
  });

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      controller: controller,
      obscureText: obscureText,
      keyboardType: keyboardType,
      maxLines: maxLines,
      style: const TextStyle(color: AppColors.primaryText),
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        errorText: errorText,
        prefixIcon: prefixIcon != null
            ? Icon(prefixIcon, color: AppColors.gold)
            : null,
        suffixIcon: suffixIcon != null
            ? IconButton(
                onPressed: onSuffixIconTap,
                icon: Icon(suffixIcon, color: AppColors.gold),
              )
            : null,
      ),
    );
  }
}

/// Custom divider with brown color
class PowerDivider extends StatelessWidget {
  final double? height;
  final double? thickness;
  final double? indent;
  final double? endIndent;

  const PowerDivider({
    super.key,
    this.height,
    this.thickness,
    this.indent,
    this.endIndent,
  });

  @override
  Widget build(BuildContext context) {
    return Divider(
      height: height,
      thickness: thickness ?? 1,
      indent: indent,
      endIndent: endIndent,
      color: AppColors.brown,
    );
  }
}

/// Custom chip widget with gold accent
class PowerChip extends StatelessWidget {
  final String label;
  final bool selected;
  final VoidCallback? onTap;
  final IconData? icon;

  const PowerChip({
    super.key,
    required this.label,
    this.selected = false,
    this.onTap,
    this.icon,
  });

  @override
  Widget build(BuildContext context) {
    return FilterChip(
      label: Text(label),
      selected: selected,
      onSelected: onTap != null ? (_) => onTap!() : null,
      avatar: icon != null ? Icon(icon, size: 18) : null,
      backgroundColor: AppColors.surface,
      selectedColor: AppColors.gold.withValues(alpha: 0.2),
      checkmarkColor: AppColors.gold,
      labelStyle: TextStyle(
        color: selected ? AppColors.background : AppColors.primaryText,
        fontWeight: selected ? FontWeight.w600 : FontWeight.w400,
      ),
      side: BorderSide(
        color: selected ? AppColors.gold : AppColors.brown,
        width: 1,
      ),
    );
  }
}

/// Custom progress indicator with gold color
class PowerProgressIndicator extends StatelessWidget {
  final double? value;
  final double strokeWidth;
  final bool linear;

  const PowerProgressIndicator({
    super.key,
    this.value,
    this.strokeWidth = 4.0,
    this.linear = false,
  });

  @override
  Widget build(BuildContext context) {
    if (linear) {
      return LinearProgressIndicator(
        value: value,
        backgroundColor: AppColors.brown,
        valueColor: const AlwaysStoppedAnimation<Color>(AppColors.gold),
      );
    }

    return CircularProgressIndicator(
      value: value,
      strokeWidth: strokeWidth,
      backgroundColor: AppColors.brown,
      valueColor: const AlwaysStoppedAnimation<Color>(AppColors.gold),
    );
  }
}
