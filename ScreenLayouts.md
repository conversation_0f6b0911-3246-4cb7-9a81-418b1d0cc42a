# PowerSights – Photo Analysis Flow (Dark Theme)

This document outlines the **screen layouts** for the new _photo-analysis_ feature that allows a user to capture (or pick) an image, confirm it, and finally view analysis results.  Everything follows the existing PowerSights dark theme and re-uses the `PowerCard`, `PowerButton`, and colour palette defined in `AppColors`.

---

## 1. Image Selection – `PhotoInputScreen`

| Element | Description |
|---------|-------------|
| **AppBar** | Title: **"New Analysis"**  – Icon colour inherits default gold accent. |
| Instruction Card | `PowerCard` (elevated).  Shows a large camera/album glyph (`photo_camera_back_outlined`, gold, 72 px) and two lines of instructional copy. |
| Primary Button | `PowerButton` — **"Take Photo"** (icon: `camera_alt`) launches system camera via `image_picker`. |
| Secondary Button | `PowerButton` (outlined) — **"Choose from Gallery"** (icon: `image_outlined`) launches file-picker/gallery. |

Layout Sketch:
```
┌ New Analysis ─────────────────────────┐
│  📷                                   │
│  Add a photo                         │
│  Choose an existing image or …       │
└───────────────────────────────────────┘

[ Take Photo ]
[ Choose from Gallery ] (outlined)
```

---

## 2. Image Confirmation – `PhotoConfirmScreen`

| Element | Description |
|---------|-------------|
| **AppBar** | Title: **"Confirm Image"** |
| Image Viewer | `Image.file` displayed in an `Expanded` area, clipped with 12 px radius.  Uses `BoxFit.contain` on black background for perfect contrast. |
| Action Buttons | Two equal-width `PowerButton`s at the bottom:  (1) **Retake** (outlined, `refresh`) which pops back;  (2) **Confirm** (filled, `check_circle`) which proceeds. |

Sketch:
```
┌ Confirm Image ───────────────────────┐
│                                      │
│          [   Photo Preview   ]       │
│                                      │
└───────────────────────────────────────┘
[ Retake ]      [ Confirm ]
```

---

## 3. Results – `PhotoResultScreen`

| Element | Description |
|---------|-------------|
| **AppBar** | Title: **"Analysis Results"** |
| Image Preview | Same presentation as confirmation screen (top of column). |
| Results Card | `PowerCard` with analytics icon (`analytics_outlined`, gold) + heading **"Placeholder Results"** and body copy _"API response will be displayed here once available."_ |
| Action Button | `PowerButton` **"New Analysis"** (icon: `add_a_photo`) clears stack and returns to `PhotoInputScreen`. |

Sketch:
```
┌ Analysis Results ─────────────────────┐
│                                      │
│          [   Photo Preview   ]        │
│                                      │
├──────────────────────────────────────┤
│  📊  Placeholder Results             │
│  API response will …                 │
└──────────────────────────────────────┘
[ New Analysis ]
```

---

### Colour & Theming Notes

* **Background** – `AppColors.background` (pure black `#000000`).
* **Primary Text** – `#FFFFFF`.
* **Accent (Gold)** – `AppColors.gold` (`#E5B611`) used for icons, active button states, and indicator colours.
* Buttons rely on the pre-configured `ElevatedButtonTheme` & `OutlinedButtonTheme` from `AppTheme.darkTheme` so no additional styling is required.

---

### Navigation Flow
```
PhotoInputScreen --(image selected)--> PhotoConfirmScreen --(confirm)--> PhotoResultScreen
            ^                                                         |
            |---------------------------------------------------------|
                                   (New Analysis / Retake)
```

These layouts are now fully implemented in code (`lib/screens/photo_*.dart`) and integrated as the app entry point in `main.dart`. Replace the placeholder results once the API becomes available. Cheers! 🚀
