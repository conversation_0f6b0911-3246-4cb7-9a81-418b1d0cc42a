# API Debugging Guide: Real vs Mock Data

This guide helps you understand when your Flutter app is using real AI analysis vs mock data, and how to debug API issues.

## 🔍 How to Tell if You're Getting Real vs Mock Data

### Visual Indicators in the App

1. **Mock Data Warning Banner**
   - When mock data is used, you'll see a yellow warning banner
   - Text: "Using Mock Data - The AI API is currently unavailable"
   - Icon: Warning amber icon

2. **Description Text Prefix**
   - **Mock data**: Starts with "⚠️ MOCK DATA:"
   - **Real data**: Starts with "AI Analysis:" (from Inference API) or no prefix (from Spaces)

3. **Confidence Score**
   - **Mock data**: Always exactly 85.0%
   - **Real data**: Varies based on actual AI confidence

4. **Detected Objects**
   - **Mock data**: Always ['mountain', 'tree', 'sky', 'landscape']
   - **Real data**: Varies based on actual image content

### Console Logs (Debug Mode)

When running in debug mode, check the console for detailed logs:

```
🔍 Starting image analysis (attempt 1)
📝 Using prompt: Analyze this image in detail...
🖼️ Image converted to base64 (12345 characters)
🤗 Trying Hugging Face Inference API with 4 models...
🌐 Attempting Inference API call to: Salesforce/blip-image-captioning-large
📡 Response received: 200
✅ Inference API call successful! Parsing response...
```

## 🚨 Common API Failure Scenarios

### 1. Network Issues
```
🔌 Network error for model 0: SocketException: Failed host lookup
```
**Solution**: Check internet connection

### 2. Rate Limiting
```
⚠️ Rate limited (429), trying next model...
```
**Solution**: Wait a few minutes or add HF token for higher limits

### 3. Model Loading
```
⚠️ Model loading (503), trying next model...
```
**Solution**: Wait for model to load (usually 1-2 minutes)

### 4. All APIs Failed
```
💥 All API endpoints failed!
🎭 Falling back to mock data
```
**Solution**: Check API status or try again later

## 🛠️ Debugging Steps

### Step 1: Check Console Logs
1. Open browser developer tools (F12)
2. Go to Console tab
3. Take a photo and watch the logs
4. Look for the emoji indicators to trace the API calls

### Step 2: Verify API Endpoints
The app tries these APIs in order:

1. **Hugging Face Inference API** (Primary)
   - `Salesforce/blip-image-captioning-large`
   - `Salesforce/blip-image-captioning-base`
   - `microsoft/git-large-coco`
   - `nlpconnect/vit-gpt2-image-captioning`

2. **Spaces API** (Fallback)
   - `https://opengvlab-internvl.hf.space`
   - `https://huggingface.co/spaces/OpenGVLab/InternVL`

### Step 3: Test Individual APIs
You can test the APIs manually:

```bash
# Test BLIP model (replace with actual image file)
curl -X POST \
  https://api-inference.huggingface.co/models/Salesforce/blip-image-captioning-large \
  -H "Content-Type: application/octet-stream" \
  --data-binary @your-image.jpg
```

### Step 4: Check API Status
- Visit [Hugging Face Status](https://status.huggingface.co/)
- Check individual model pages for status
- Look for maintenance announcements

## ⚙️ Configuration Options

### Enable/Disable Features
Edit `lib/config/api_config.dart`:

```dart
// Disable fallback to mock data (will show errors instead)
static const bool enableFallbackToMockData = false;

// Disable retry logic
static const bool enableRetryOnFailure = false;

// Reduce retry attempts
static const int maxRetryAttempts = 1;
```

### Add Your Hugging Face Token
For better rate limits, add your token:

```dart
// In the API service, uncomment and add your token:
'Authorization': 'Bearer YOUR_HF_TOKEN_HERE',
```

### Add More Models
Add more vision models to try:

```dart
static const List<String> visionLanguageModels = [
  'Salesforce/blip-image-captioning-large',
  'Salesforce/blip-image-captioning-base',
  'microsoft/git-large-coco',
  'nlpconnect/vit-gpt2-image-captioning',
  'your-custom-model-here',  // Add your model
];
```

## 📊 Understanding API Responses

### Inference API Response Format
```json
[
  {
    "generated_text": "a photo of a cat sitting on a table"
  }
]
```

### Spaces API Response Format
```json
{
  "data": [
    "This image shows a detailed analysis of the scene..."
  ]
}
```

### Mock Data Response
```dart
VisionAnalysisResult(
  description: "⚠️ MOCK DATA: This image shows a beautiful landscape...",
  confidence: 0.85,
  detectedObjects: ['mountain', 'tree', 'sky', 'landscape'],
  colors: ['blue', 'green', 'brown'],
  tags: ['outdoor', 'nature', 'landscape', 'bright'],
  isMockData: true,
)
```

## 🔧 Troubleshooting Common Issues

### Issue: Always Getting Mock Data
**Possible Causes:**
1. No internet connection
2. All APIs are down
3. Rate limiting
4. CORS issues (web only)

**Solutions:**
1. Check network connectivity
2. Wait and try again
3. Add HF token for higher limits
4. Test on mobile/desktop instead of web

### Issue: Slow Response Times
**Possible Causes:**
1. Model loading time
2. Large image size
3. Network latency

**Solutions:**
1. Wait for models to warm up
2. Reduce image size in image picker settings
3. Try different models

### Issue: Poor Analysis Quality
**Possible Causes:**
1. Using basic models (BLIP-base)
2. Low-quality images
3. Unsupported image types

**Solutions:**
1. Ensure large models are tried first
2. Use high-quality, well-lit images
3. Stick to common image formats (JPEG, PNG)

## 📈 Performance Optimization

### Image Size Optimization
Current settings in `photo_input_screen.dart`:
```dart
maxWidth: 1800,
maxHeight: 1800,
imageQuality: 85,
```

### Model Priority
Models are tried in order of capability:
1. `blip-image-captioning-large` (best quality)
2. `blip-image-captioning-base` (faster)
3. `git-large-coco` (alternative)
4. `vit-gpt2-image-captioning` (fallback)

### Caching Considerations
- No caching implemented (each request is fresh)
- Consider adding local caching for repeated images
- API responses are not stored locally

## 🎯 Success Indicators

You know the real API is working when you see:

1. ✅ Console logs showing successful API calls
2. 🎯 No mock data warning banner
3. 📊 Varying confidence scores
4. 🏷️ Relevant detected objects for your image
5. 🎨 Appropriate color analysis
6. 📝 Contextual description matching your image

## 🆘 Getting Help

If you're still having issues:

1. **Check the logs** - Console output shows exactly what's happening
2. **Test manually** - Use curl to test API endpoints directly
3. **Verify image format** - Ensure images are valid JPEG/PNG
4. **Check HF status** - Visit Hugging Face status page
5. **Try different models** - Some models may be more reliable than others

The app is designed to be resilient and will always show something to the user, whether it's real AI analysis or mock data for demonstration purposes.
